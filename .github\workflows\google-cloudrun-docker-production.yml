name: Build and Deploy to Cloud Run

on:
  push:
    branches: ['production']
  workflow_dispatch:

env:
  PROJECT_ID: marutitech-website-production
  GAR_LOCATION: europe-west2
  SERVICE: redesign-maruti-site-production
  REGION: asia-southeast1
  IMAGE: production
  REGISTRY: mtl-nextjs-site

jobs:
  deploy:
    permissions:
      contents: 'read'
      id-token: 'write'

    runs-on: ubuntu-latest
    steps:
      - name: Check if author needs to be replaced
        id: check_author
        run: |
          if [ "${{ github.actor }}" = "24072012" ]; then
            echo "::set-output name=author::Strapi"
          else
            echo "::set-output name=author::${{ github.actor }}"
          fi
        shell: bash

      - name: Notify Deployment startup to Slack
        uses: slackapi/slack-github-action@v1.23.0
        with:
          channel-id: 'C05EU6F4EG6'
          payload: |
            {
              "text":  "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "_Event: Deployment Started_ :arrows_counterclockwise: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Checkout
        uses: actions/checkout@v4.1.7

      - name: Google Auth
        id: auth
        uses: 'google-github-actions/auth@v2'
        with:
          token_format: 'access_token'
          workload_identity_provider: '${{ secrets.WIF_PROVIDER }}'
          service_account: '${{ secrets.WIF_SERVICE_ACCOUNT }}'

      - id: secrets
        name: Fetch secrets from GCP
        uses: google-github-actions/get-secretmanager-secrets@v1
        with:
          secrets: |-
            NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID
            NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID
            NEXT_PUBLIC_STRAPI_URL:${{ env.PROJECT_ID }}/NEXT_PUBLIC_STRAPI_URL
            NEXT_PUBLIC_SITE_URL:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SITE_URL
            NEXT_PUBLIC__GOOGLE_TAG_MANAGER:${{ env.PROJECT_ID }}/NEXT_PUBLIC__GOOGLE_TAG_MANAGER
            NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION:${{ env.PROJECT_ID }}/NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION
            NEXT_PUBLIC__ALGOLIA_APP_ID:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_APP_ID
            NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES
            NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES:${{ env.PROJECT_ID }}/NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES
            NEXT_PUBLIC_HUBSPOT_API_KEY:${{ env.PROJECT_ID }}/NEXT_PUBLIC_HUBSPOT_API_KEY
            NEXT_PUBLIC_HUBSPOT_PORTAL_ID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_HUBSPOT_PORTAL_ID
            NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID
            NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID
            NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL
            NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL
            NEXT_PUBLIC_SENDGRID_API_KEY:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SENDGRID_API_KEY
            NEXT_PUBLIC_MAIL_TO:${{ env.PROJECT_ID }}/NEXT_PUBLIC_MAIL_TO
            NEXT_PUBLIC_MAIL_FROM:${{ env.PROJECT_ID }}/NEXT_PUBLIC_MAIL_FROM
            NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID
            NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID
            NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID:${{ env.PROJECT_ID }}/NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID
      # - name: Print secret value length
      #   env:
      #     NEXT_PUBLIC_STRAPI_URL: ${{ steps.secrets.outputs.NEXT_PUBLIC_STRAPI_URL }}
      #   run: |
      #     echo "Length of NEXT_PUBLIC_STRAPI_URL: ${#NEXT_PUBLIC_STRAPI_URL}"

      - name: Docker Auth
        id: docker-auth
        uses: 'docker/login-action@v3.3.0'
        with:
          username: 'oauth2accesstoken'
          password: '${{ steps.auth.outputs.access_token }}'
          registry: '${{ env.GAR_LOCATION }}-docker.pkg.dev'

      - name: Build and Push Container
        env:
          NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID: ${{ steps.secrets.outputs.NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID }}
          NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID: ${{ steps.secrets.outputs.NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID }}
          NEXT_PUBLIC_STRAPI_URL: ${{ steps.secrets.outputs.NEXT_PUBLIC_STRAPI_URL }}
          NEXT_PUBLIC_SITE_URL: ${{ steps.secrets.outputs.NEXT_PUBLIC_SITE_URL }}
          NEXT_PUBLIC__GOOGLE_TAG_MANAGER: ${{ steps.secrets.outputs.NEXT_PUBLIC__GOOGLE_TAG_MANAGER }}
          NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION: ${{ steps.secrets.outputs.NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION }}
          NEXT_PUBLIC__ALGOLIA_APP_ID: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_APP_ID }}
          NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES }}
          NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES: ${{ steps.secrets.outputs.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES }}
          NEXT_PUBLIC_HUBSPOT_API_KEY: ${{ steps.secrets.outputs.NEXT_PUBLIC_HUBSPOT_API_KEY }}
          NEXT_PUBLIC_HUBSPOT_PORTAL_ID: ${{ steps.secrets.outputs.NEXT_PUBLIC_HUBSPOT_PORTAL_ID }}
          NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID: ${{ steps.secrets.outputs.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID }}
          NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID: ${{ steps.secrets.outputs.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID }}
          NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL: ${{ steps.secrets.outputs.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL }}
          NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL: ${{ steps.secrets.outputs.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL }}
          NEXT_PUBLIC_SENDGRID_API_KEY: ${{ steps.secrets.outputs.NEXT_PUBLIC_SENDGRID_API_KEY }}
          NEXT_PUBLIC_MAIL_TO: ${{ steps.secrets.outputs.NEXT_PUBLIC_MAIL_TO }}
          NEXT_PUBLIC_MAIL_FROM: ${{ steps.secrets.outputs.NEXT_PUBLIC_MAIL_FROM }}
          NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID: ${{ steps.secrets.outputs.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID }}
          NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: ${{ steps.secrets.outputs.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID }}
          NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID: ${{ steps.secrets.outputs.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID }}
        run: |-
          # docker build -t "${{ env.GAR_LOCATION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REGISTRY }}/${{ env.IMAGE }}:${{ github.sha }}" -f .docker/Dockerfile .

          docker build --no-cache \
          --build-arg NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID \
          --build-arg NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_TEMPLATE_ID \
          --build-arg NEXT_PUBLIC_STRAPI_URL=$NEXT_PUBLIC_STRAPI_URL \
          --build-arg NEXT_PUBLIC_SITE_URL=$NEXT_PUBLIC_SITE_URL \
          --build-arg NEXT_PUBLIC__GOOGLE_TAG_MANAGER=$NEXT_PUBLIC__GOOGLE_TAG_MANAGER \
          --build-arg NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION=$NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION \
          --build-arg NEXT_PUBLIC__ALGOLIA_APP_ID=$NEXT_PUBLIC__ALGOLIA_APP_ID \
          --build-arg NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY=$NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES \
          --build-arg NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES=$NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES \
          --build-arg NEXT_PUBLIC_HUBSPOT_API_KEY=$NEXT_PUBLIC_HUBSPOT_API_KEY \
          --build-arg NEXT_PUBLIC_HUBSPOT_PORTAL_ID=$NEXT_PUBLIC_HUBSPOT_PORTAL_ID \
          --build-arg NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID=$NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID \
          --build-arg NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID=$NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID \
          --build-arg NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL=$NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL \
          --build-arg NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL=$NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL \
          --build-arg NEXT_PUBLIC_SENDGRID_API_KEY=$NEXT_PUBLIC_SENDGRID_API_KEY \
          --build-arg NEXT_PUBLIC_MAIL_TO=$NEXT_PUBLIC_MAIL_TO \
          --build-arg NEXT_PUBLIC_MAIL_FROM=$NEXT_PUBLIC_MAIL_FROM \
          --build-arg NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID=$NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID \
          --build-arg NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID=$NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID \
          --build-arg NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID=$NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID \
          -t "${{ env.GAR_LOCATION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REGISTRY }}/${{ env.IMAGE }}:${{ github.sha }}" \
          -f .docker/Dockerfile .
          
          docker push "${{ env.GAR_LOCATION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REGISTRY }}/${{ env.IMAGE }}:${{ github.sha }}"

      - name: Deploy to Cloud Run
        id: deploy
        uses: google-github-actions/deploy-cloudrun@v2.6.0
        with:
          service: ${{ env.SERVICE }}
          region: ${{ env.REGION }}
          image: ${{ env.GAR_LOCATION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REGISTRY }}/${{ env.IMAGE }}:${{ github.sha }}
          # env_vars: |
          #   NEXT_PUBLIC_STRAPI_URL=${{ secrets.NEXT_PUBLIC_STRAPI_URL_PRODUCTION }}
          #   NEXT_PUBLIC_SITE_URL=${{ secrets.NEXT_PUBLIC_SITE_URL_PRODUCTION }}
          #   NEXT_PUBLIC__GOOGLE_TAG_MANAGER=${{ secrets.NEXT_PUBLIC__GOOGLE_TAG_MANAGER_PRODUCTION }}
          #   NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION=${{ secrets.NEXT_PUBLIC__GOOGLE_SITE_VERIFICATION_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_APP_ID=${{ secrets.NEXT_PUBLIC__ALGOLIA_APP_ID_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY=${{ secrets.NEXT_PUBLIC__ALGOLIA_PUBLIC_API_KEY_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OVERALL_SITE_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_ASC_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_BLOGS_DESC_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_CASE_STUDIES_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_SERVICE_PAGES_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_INDUSTRY_PAGES_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_PARTNER_PAGES_PRODUCTION }}
          #   NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES=${{ secrets.NEXT_PUBLIC__ALGOLIA_INDEX_FOR_OTHER_PAGES_PRODUCTION }}
          #   NEXT_PUBLIC_HUBSPOT_API_KEY=${{ secrets.NEXT_PUBLIC_HUBSPOT_API_KEY_PRODUCTION }}
          #   NEXT_PUBLIC_HUBSPOT_PORTAL_ID=${{ secrets.NEXT_PUBLIC_HUBSPOT_PORTAL_ID_PRODUCTION }}
          #   NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID=${{ secrets.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID_PRODUCTION }}
          #   NEXT_PUBLIC_SENDGRID_API_KEY=${{ secrets.NEXT_PUBLIC_SENDGRID_API_KEY_PRODUCTION }}
          #   NEXT_PUBLIC_MAIL_TO=${{ secrets.NEXT_PUBLIC_MAIL_TO_PRODUCTION }}
          #   NEXT_PUBLIC_MAIL_FROM=${{ secrets.NEXT_PUBLIC_MAIL_FROM_PRODUCTION }}
          #   NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID=${{ secrets.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID_PRODUCTION }}
          #   NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID=${{ secrets.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID_PRODUCTION }}
          #   NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL=${{ secrets.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL_PRODUCTION }}
          #   NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL=${{ secrets.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL_PRODUCTION }}

      - name: Notify Deployment completion to Slack
        uses: slackapi/slack-github-action@v1.23.0
        with:
          channel-id: 'C05EU6F4EG6'
          payload: |
            {
              "text":  "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "_Event: Deployment Completed_ :rocket: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Notify Deployment failure to Slack
        if: failure()
        uses: slackapi/slack-github-action@v1.23.0
        with:
          channel-id: 'C05EU6F4EG6'
          payload: |
            {
              "text":  "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "_Event: Deployment Failed_ :rotating_light: \n> *Repository Name:* ${{ github.event.repository.name }}\n> *Branch:* ${{ github.ref_name }}\n> *Author:* ${{ steps.check_author.outputs.author }}\n> *Commit Url:* ${{ github.event.head_commit.url }}\n> *Workflow Url:* ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Show Output
        run: echo ${{ steps.deploy.outputs.url }}

