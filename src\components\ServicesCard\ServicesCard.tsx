'use client';

import React, { useState, useEffect } from 'react';
import { Col, Container, Row } from 'react-bootstrap';
import Image from 'next/image';
import Autoplay from 'embla-carousel-autoplay';
import useEmblaCarousel from 'embla-carousel-react';
import CircularButtonWithArrow from '@components/CircularButtonWithArrow';
import DotButton from '@components/DotButton/DotButton';
import Heading from '@components/Heading';
import Link from '@components/Link';
import useDotButton from '@hooks/useDotButton';
import usePrevNextButtons from '@hooks/usePrevNextButtons';
import PrevButton from '@components/SliderButtons/PrevButton';
import NextButton from '@components/SliderButtons/NextButton';
import useMediaQueryState from '@hooks/useMediaQueryState';
import classNames from '@utils/classNames';
import breakpoints from '@styles/breakpoints.module.css';
import variables from '@styles/variables.module.css';
import emblastyles from '../../styles/emlaDots.module.css';

import styles from './ServicesCard.module.css';
import {
  CardTypes,
  L2ServicesCardTypes,
  L2ServiceType,
  ServicesCardTypes,
} from './types';

export default function ServicesCard({
  data,
  variant = 'showTextOnHover',
  l2ServiceData,
  shouldRedirectToL3 = false,
  background,
  variantWhite = true,
  layoutType = 'layout_1',
}: {
  data?: ServicesCardTypes;
  variant?: 'showTextOnHover' | 'blackSlideCard' | 'whiteSlideCard';
  l2ServiceData?: L2ServiceType;
  shouldRedirectToL3?: boolean;
  background?: string;
  variantWhite?: boolean;
  layoutType?: string;
}) {
  const [isActiveId, setIsActiveId] = useState(1);

  const isLaptop = useMediaQueryState({
    query: `(max-width: ${breakpoints['breakpoint-xl-1024']})`,
  });

  const forDesktop = useMediaQueryState({
    query: '(min-width: 1025px) and (max-width: 1222px)',
  });

  const cardStyle = id => ({
    width: forDesktop
      ? isActiveId === id
        ? '280px'
        : '140px'
      : !isLaptop && isActiveId === id
        ? '384px'
        : '182px',
    height:
      // eslint-disable-next-line no-nested-ternary
      isLaptop && isActiveId === id ? '392px' : !isLaptop ? '392px' : '392px',
    transition: !isLaptop ? '0.8s width' : 'none',
  });

  const mouseEnterHandler = id => {
    setIsActiveId(id);
  };

  // for black service card variant
  const [emblaRef, emblaApi] = useEmblaCarousel(
    { align: 'start', dragFree: true },
    [
      Autoplay({
        playOnInit: true,
        delay: 6000,
        stopOnFocusIn: true,
        stopOnInteraction: false,
        stopOnMouseEnter: true,
      }),
    ],
  );

  const { selectedIndex, scrollSnaps, onDotButtonClick } =
    useDotButton(emblaApi);

  const {
    prevBtnDisabled,
    nextBtnDisabled,
    onPrevButtonClick,
    onNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const {
    onPrevButtonClick: onImgPrevButtonClick,
    onNextButtonClick: onImgNextButtonClick,
  } = usePrevNextButtons(emblaApi);

  const onPrev = () => {
    onPrevButtonClick();
    onImgPrevButtonClick();
  };

  const onNext = () => {
    onNextButtonClick();
    onImgNextButtonClick();
  };

  const isCarousel = l2ServiceData?.L2ServicesCard.length >= 4;

  return (
    <>
      {variant === 'showTextOnHover' && (
        <section className={styles.ourServiceSection}>
          <div className={styles.title_description_container}>
            <Heading
              headingType="h2"
              title={data?.title}
              position="center"
              className={styles.ourServiceTitle}
            />
            <div
              className={styles.ourServiceSubtitle}
              // eslint-disable-next-line react/no-danger
              dangerouslySetInnerHTML={{ __html: data?.subtitle }}
            ></div>
          </div>

          <Container fluid className={styles.ourServiceContainer}>
            <div className={classNames(styles.serviceRow, styles.row)}>
              {data?.ourServicesCard?.map((card: CardTypes) => (
                <Col className={styles.cardCol} key={card?.id}>
                  <Link className={styles.link} href={card?.url}>
                    <div
                      onMouseEnter={() => mouseEnterHandler(card?.id)}
                      className={styles.cardContainer}
                      style={cardStyle(card?.id)}
                    >
                      <Image
                        src={card?.cardImage?.data?.attributes?.url}
                        width={384}
                        height={392}
                        alt={
                          card?.cardImage?.data?.attributes?.alternativeText ||
                          card?.cardImage?.data?.attributes?.name
                        }
                        className={styles.serviceImage}
                        style={
                          !isLaptop
                            ? { transition: '0.8s width' }
                            : { transition: '0.8s height' }
                        }
                      />
                      <div
                        className={styles.backdrop}
                        style={
                          !isLaptop
                            ? isActiveId === card?.id
                              ? {
                                  backgroundColor: 'rgb(0, 0, 0, 0.7)',
                                }
                              : {
                                  backgroundColor: 'rgb(255, 255, 255, 0.7)',
                                }
                            : {}
                        }
                      />
                      <div
                        className={`${styles.cardBody} ${!isLaptop ? (isActiveId === card?.id ? styles.visible : '') : styles.visible}`}
                      >
                        <Heading
                          headingType="h3"
                          title={card?.cardTitle}
                          className={styles.cardTitle}
                        />
                        <div
                          className={styles.cardDescription}
                          // eslint-disable-next-line react/no-danger
                          dangerouslySetInnerHTML={{
                            __html: card?.cardContent,
                          }}
                        />
                      </div>
                    </div>
                    <div>
                      <p className={styles.cardTag}>{card?.cardTitle2}</p>
                    </div>
                  </Link>
                </Col>
              ))}
            </div>
          </Container>
        </section>
      )}
      {variant === 'blackSlideCard' && (
        <section
          className={styles.blackSlideCardWrapper}
          style={
            background === 'black'
              ? { backgroundColor: variables['colorBlack'] }
              : null
          }
        >
          <Heading
            headingType="h2"
            title={l2ServiceData?.title}
            style={
              background === 'black' ? { color: variables['colorWhite'] } : null
            }
            position="center"
            className={styles.blackSlideTitle}
          />
          <div className={styles.blackSlideContainer}>
            {isCarousel && (
              <div className={styles.embla}>
                <div className={styles.embla__viewport} ref={emblaRef}>
                  <div className={classNames(styles.embla__container)}>
                    {l2ServiceData?.L2ServicesCard?.map(
                      (cardData: L2ServicesCardTypes) => (
                        <div className={styles.embla__slide} key={cardData?.id}>
                          <div
                            className={styles.embla__slide__number}
                            key={cardData?.id}
                          >
                            {cardData?.service_page_link ? (
                              <Link
                                href={cardData?.service_page_link}
                                className={styles.servicePageLink}
                              >
                                <div className={styles.blackSlideCard}>
                                  <div className={styles.blackSlideCardContent}>
                                    <Image
                                      src={
                                        cardData?.on_hover_bg_image?.data
                                          ?.attributes?.url
                                      }
                                      height={383}
                                      width={256}
                                      alt="gradient"
                                      className={styles.gradientImage}
                                    />
                                    <Heading
                                      headingType="h3"
                                      title={cardData?.title}
                                      className={styles.blackSlideCardTitle}
                                    />
                                    <span
                                      className={
                                        styles.blackSlideCardDescription
                                      }
                                      // eslint-disable-next-line react/no-danger
                                      dangerouslySetInnerHTML={{
                                        __html: cardData?.description,
                                      }}
                                    />
                                    {shouldRedirectToL3 &&
                                      layoutType !== 'layout_2' && (
                                        <div className={styles.arrow_button}>
                                          <CircularButtonWithArrow variant="small" />
                                        </div>
                                      )}
                                  </div>
                                </div>
                              </Link>
                            ) : (
                              <div className={styles.servicePageLink}>
                                <div className={styles.blackSlideCard}>
                                  <div className={styles.blackSlideCardContent}>
                                    <Image
                                      src={
                                        cardData?.on_hover_bg_image?.data
                                          ?.attributes?.url
                                      }
                                      height={383}
                                      width={256}
                                      alt="gradient"
                                      className={styles.gradientImage}
                                    />
                                    <Heading
                                      headingType="h3"
                                      title={cardData?.title}
                                      className={styles.blackSlideCardTitle}
                                    />
                                    <span
                                      className={
                                        styles.blackSlideCardDescription
                                      }
                                      // eslint-disable-next-line react/no-danger
                                      dangerouslySetInnerHTML={{
                                        __html: cardData?.description,
                                      }}
                                    />
                                    {shouldRedirectToL3 &&
                                      layoutType !== 'layout_2' && (
                                        <div className={styles.arrow_button}>
                                          <CircularButtonWithArrow variant="small" />
                                        </div>
                                      )}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                </div>
                <div className={styles.embla__controls}>
                  <div className={emblastyles.embla__dots}>
                    {scrollSnaps.length > 1 &&
                      scrollSnaps.map((_, index) => (
                        <DotButton
                          key={index}
                          onClick={() => onDotButtonClick(index)}
                          className={
                            index === selectedIndex
                              ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                              : variantWhite
                                ? classNames(
                                    emblastyles.embla__dot,
                                    emblastyles.embla__dot_bg_white,
                                  )
                                : emblastyles.embla__dot
                          }
                        />
                      ))}
                  </div>
                </div>
              </div>
            )}
            {!isCarousel && (
              <div className={classNames(styles.withoutCarouselContainer)}>
                {l2ServiceData?.L2ServicesCard?.map(
                  (cardData: L2ServicesCardTypes, index) => (
                    <>
                      {cardData?.service_page_link ? (
                        <Link
                          href={cardData?.service_page_link}
                          className={styles.servicePageLink}
                          key={index}
                        >
                          <div className={styles.blackSlideCard}>
                            <div className={styles.blackSlideCardContent}>
                              <Image
                                src={
                                  cardData?.on_hover_bg_image?.data?.attributes
                                    ?.url
                                }
                                height={383}
                                width={256}
                                alt="gradient"
                                className={styles.gradientImage}
                              />
                              <Heading
                                headingType="h3"
                                title={cardData?.title}
                                className={styles.blackSlideCardTitle}
                              />
                              <span
                                className={styles.blackSlideCardDescription}
                                // eslint-disable-next-line react/no-danger
                                dangerouslySetInnerHTML={{
                                  __html: cardData?.description,
                                }}
                              />
                              {shouldRedirectToL3 &&
                                layoutType !== 'layout_2' && (
                                  <div className={styles.arrow_button}>
                                    <CircularButtonWithArrow variant="small" />
                                  </div>
                                )}
                            </div>
                          </div>
                        </Link>
                      ) : (
                        <div className={styles.servicePageLink} key={index}>
                          <div className={styles.blackSlideCard}>
                            <div className={styles.blackSlideCardContent}>
                              <Image
                                src={
                                  cardData?.on_hover_bg_image?.data?.attributes
                                    ?.url
                                }
                                height={383}
                                width={256}
                                alt="gradient"
                                className={styles.gradientImage}
                              />
                              <Heading
                                headingType="h3"
                                title={cardData?.title}
                                className={styles.blackSlideCardTitle}
                              />
                              <span
                                className={styles.blackSlideCardDescription}
                                // eslint-disable-next-line react/no-danger
                                dangerouslySetInnerHTML={{
                                  __html: cardData?.description,
                                }}
                              />
                              {shouldRedirectToL3 &&
                                layoutType !== 'layout_2' && (
                                  <div className={styles.arrow_button}>
                                    <CircularButtonWithArrow variant="small" />
                                  </div>
                                )}
                            </div>
                          </div>
                        </div>
                      )}
                    </>

                    // </div>
                  ),
                )}
              </div>
            )}
          </div>
        </section>
      )}
      {variant === 'whiteSlideCard' && (
        <section
          className={classNames(
            styles.blackSlideCardWrapper,
            styles.whiteSlideCardWrapper,
          )}
          style={
            background === 'black'
              ? { backgroundColor: variables['colorBlack'] }
              : null
          }
        >
          <div className={styles.headingContainer}>
            <Heading
              headingType="h2"
              title={l2ServiceData?.title}
              style={
                background === 'black'
                  ? { color: variables['colorWhite'] }
                  : null
              }
              position="center"
              className={styles.blackSlideTitle}
            />
            <div className={styles.carouselArrowButtons}>
              <PrevButton onClick={onPrev} disabled={prevBtnDisabled} />
              <NextButton onClick={onNext} disabled={nextBtnDisabled} />
            </div>
          </div>

          <div className={styles.blackSlideContainer}>
            {isCarousel && (
              <div className={styles.embla}>
                <div className={styles.embla__viewport} ref={emblaRef}>
                  <div className={classNames(styles.embla__container)}>
                    {l2ServiceData?.L2ServicesCard?.map(
                      (cardData: L2ServicesCardTypes) => (
                        <div className={styles.embla__slide} key={cardData?.id}>
                          <div
                            className={styles.embla__slide__number}
                            key={cardData?.id}
                          >
                            <div className={styles.servicePageLink}>
                              <div
                                className={classNames(
                                  styles.blackSlideCard,
                                  styles.whiteSlideCard,
                                )}
                              >
                                <div className={styles.blackSlideCardContent}>
                                  <Image
                                    src={
                                      cardData?.on_hover_bg_image?.data
                                        ?.attributes?.url
                                    }
                                    height={383}
                                    width={256}
                                    alt="gradient"
                                    className={styles.gradientImage}
                                  />
                                  <Heading
                                    headingType="h3"
                                    title={cardData?.title}
                                    className={styles.whiteSlideCardTitle}
                                  />
                                  <span
                                    className={styles.whiteSlideCardDescription}
                                    // eslint-disable-next-line react/no-danger
                                    dangerouslySetInnerHTML={{
                                      __html: cardData?.description,
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ),
                    )}
                  </div>
                </div>
                <div className={styles.embla__controls_whiteslide}>
                  <div className={emblastyles.embla__dots}>
                    {scrollSnaps.length > 1 &&
                      scrollSnaps.map((_, index) => (
                        <DotButton
                          key={index}
                          onClick={() => onDotButtonClick(index)}
                          className={
                            index === selectedIndex
                              ? `${emblastyles.embla__dot} ${emblastyles.embla__dot_selected}`
                              : variantWhite
                                ? classNames(
                                    emblastyles.embla__dot,
                                    emblastyles.embla__dot_bg_white,
                                  )
                                : emblastyles.embla__dot
                          }
                        />
                      ))}
                  </div>
                </div>
              </div>
            )}
            {!isCarousel && (
              <div className={classNames(styles.withoutCarouselContainer)}>
                {l2ServiceData?.L2ServicesCard?.map(
                  (cardData: L2ServicesCardTypes, index) => (
                    <div className={styles.servicePageLink} key={index}>
                      <div className={styles.blackSlideCard}>
                        <div className={styles.blackSlideCardContent}>
                          <Image
                            src={
                              cardData?.on_hover_bg_image?.data?.attributes?.url
                            }
                            height={383}
                            width={256}
                            alt="gradient"
                            className={styles.gradientImage}
                          />
                          <Heading
                            headingType="h3"
                            title={cardData?.title}
                            className={styles.blackSlideCardTitle}
                          />
                          <span
                            className={styles.blackSlideCardDescription}
                            // eslint-disable-next-line react/no-danger
                            dangerouslySetInnerHTML={{
                              __html: cardData?.description,
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    // </div>
                  ),
                )}
              </div>
            )}
          </div>
        </section>
      )}
    </>
  );
}
